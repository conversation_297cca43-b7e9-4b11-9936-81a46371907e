<script lang="ts">
  import { page } from '$app/stores';
  import { ChevronRight, Home } from 'lucide-svelte';
  import { cn } from '$lib/utils';

  interface BreadcrumbItem {
    label: string;
    href?: string;
    icon?: any;
  }

  let { 
    items = [],
    showHome = true,
    className = '',
    separator = ChevronRight
  }: {
    items?: BreadcrumbItem[];
    showHome?: boolean;
    className?: string;
    separator?: any;
  } = $props();

  // Auto-generate breadcrumbs from current route if no items provided
  $: autoItems = $derived(() => {
    if (items.length > 0) return items;
    
    const pathSegments = $page.url.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];
    
    if (showHome) {
      breadcrumbs.push({
        label: 'Dashboard',
        href: '/dashboard',
        icon: Home
      });
    }
    
    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Skip the first 'dashboard' segment if showHome is true
      if (showHome && segment === 'dashboard') return;
      
      const isLast = index === pathSegments.length - 1;
      const label = segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');
      
      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentPath
      });
    });
    
    return breadcrumbs;
  });
</script>

<nav aria-label="Breadcrumb" class={cn('flex items-center space-x-1 text-sm', className)}>
  <ol class="flex items-center space-x-1">
    {#each autoItems as item, index}
      <li class="flex items-center">
        {#if index > 0}
          <svelte:component this={separator} class="text-muted-foreground mx-2 h-4 w-4" />
        {/if}
        
        {#if item.href}
          <a 
            href={item.href}
            class="text-muted-foreground hover:text-foreground flex items-center gap-1.5 transition-colors"
          >
            {#if item.icon}
              <svelte:component this={item.icon} class="h-4 w-4" />
            {/if}
            {item.label}
          </a>
        {:else}
          <span class="text-foreground flex items-center gap-1.5 font-medium">
            {#if item.icon}
              <svelte:component this={item.icon} class="h-4 w-4" />
            {/if}
            {item.label}
          </span>
        {/if}
      </li>
    {/each}
  </ol>
</nav>
