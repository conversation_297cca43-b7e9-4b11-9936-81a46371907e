<script lang="ts">
  import { cn } from '$lib/utils';
  import Breadcrumbs from './Breadcrumbs.svelte';

  interface BreadcrumbItem {
    label: string;
    href?: string;
    icon?: any;
  }

  let {
    title,
    description = '',
    breadcrumbs = [],
    showBreadcrumbs = true,
    actions,
    className = '',
    children
  }: {
    title: string;
    description?: string;
    breadcrumbs?: BreadcrumbItem[];
    showBreadcrumbs?: boolean;
    actions?: any;
    className?: string;
    children?: any;
  } = $props();
</script>

<div class={cn('space-y-4 p-6 pb-4', className)}>
  {#if showBreadcrumbs}
    <Breadcrumbs items={breadcrumbs} />
  {/if}
  
  <div class="flex items-center justify-between">
    <div class="space-y-1">
      <h1 class="text-2xl font-semibold tracking-tight">{title}</h1>
      {#if description}
        <p class="text-muted-foreground text-sm">{description}</p>
      {/if}
    </div>
    
    {#if actions}
      <div class="flex items-center gap-2">
        {@render actions()}
      </div>
    {/if}
  </div>
  
  {#if children}
    {@render children()}
  {/if}
</div>
