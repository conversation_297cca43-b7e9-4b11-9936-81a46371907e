import { redirect } from '@sveltejs/kit';
import { prisma } from '$lib/server/prisma';

export const load = async ({ locals, url }) => {
  const user = locals.user;

  if (!user) {
    throw redirect(302, '/auth/sign-in');
  }

  // Get all profiles for the user
  const profiles = await prisma.profile.findMany({
    where: {
      OR: [
        { userId: user.id },
        {
          team: {
            members: {
              some: { userId: user.id },
            },
          },
        },
      ],
    },
    include: {
      data: true,
      team: true,
      // Include the default document (resume)
      defaultDocument: true,
    },
  });

  // Get the selected profile ID from the URL or use the first profile
  const selectedProfileId =
    url.searchParams.get('profileId') || (profiles.length > 0 ? profiles[0].id : null);

  // If no profile is selected or available, return empty matches
  if (!selectedProfileId) {
    return {
      user,
      profiles,
      selectedProfileId: null,
      matches: [],
      pagination: {
        page: 1,
        limit: 20,
        totalCount: 0,
        totalPages: 0,
        hasMore: false,
      },
    };
  }

  // Get pagination parameters
  const page = parseInt(url.searchParams.get('page') || '1');
  const limit = parseInt(url.searchParams.get('limit') || '20');
  const skip = (page - 1) * limit;

  // Get job matches for the selected profile
  const matches = await prisma.job_match_result.findMany({
    where: {
      userId: user.id,
      profileId: selectedProfileId,
    },
    include: {
      job_listing: true,
    },
    orderBy: {
      matchScore: 'desc',
    },
    skip,
    take: limit,
  });

  // Get total count for pagination
  const totalCount = await prisma.job_match_result.count({
    where: {
      userId: user.id,
      profileId: selectedProfileId,
    },
  });

  // Get job alerts for the user
  const jobAlerts = await prisma.jobAlert.findMany({
    where: {
      userId: user.id,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // Get AI recommendations (top matches that weren't from alerts)
  const recommendations = await prisma.job_match_result.findMany({
    where: {
      userId: user.id,
      profileId: selectedProfileId,
      // Only get matches with high scores that weren't from alerts
      matchScore: {
        gte: 0.8,
      },
    },
    include: {
      job_listing: true,
    },
    orderBy: {
      matchScore: 'desc',
    },
    take: 10,
  });

  // Get saved jobs
  const savedJobs = await prisma.savedJob.findMany({
    where: {
      userId: user.id,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  // Fetch the job listings for the saved jobs
  const jobIds = savedJobs.map((job) => job.jobId);

  // Get job listings from cron schema
  const jobListings =
    jobIds.length > 0
      ? await prisma.job_listing.findMany({
          where: {
            id: {
              in: jobIds,
            },
          },
        })
      : [];

  // Combine saved jobs with job listings
  const savedJobsWithListings = savedJobs.map((savedJob) => {
    const jobListing = jobListings.find((job) => job.id === savedJob.jobId);
    return {
      ...savedJob,
      job_listing: jobListing || null,
    };
  });

  // Add mock data if no matches found (for UI testing)
  const mockMatches =
    matches.length === 0
      ? [
          {
            id: 'mock-1',
            userId: user.id,
            jobId: 'job-1',
            profileId: selectedProfileId,
            matchScore: 0.95,
            applied: false,
            createdAt: new Date(),
            job_listing: {
              id: 'job-1',
              platform: 'linkedin',
              jobId: 'linkedin-123',
              title: 'Senior Software Engineer',
              company: 'TechCorp Inc.',
              location: 'San Francisco, CA',
              url: 'https://example.com/job1',
              isActive: true,
              isProcessing: false,
              createdAt: new Date(),
              lastCheckedAt: new Date(),
              employmentType: 'Full-time',
              remoteType: 'Remote',
              experienceLevel: 'Senior',
              description: 'We are looking for a senior software engineer...',
              postedDate: new Date(),
              closedAt: null,
              applyLink: 'https://example.com/apply1',
              benefits: ['Health Insurance', '401k', 'Remote Work'],
              requirements: ['React', 'Node.js', '5+ years experience'],
              salary: '$120,000 - $160,000',
              salaryMin: 120000,
              salaryMax: 160000,
              yearsOfExperience: 5,
              companySize: 'Medium',
              industryTags: ['Technology', 'Software'],
              techStack: ['React', 'Node.js', 'TypeScript'],
              normalizedTitle: 'senior software engineer',
              seniorityLevel: 'Senior',
              isRemoteFriendly: true,
              requirementScore: 85,
              popularityScore: 4.2,
              lastMatchedAt: new Date(),
              matchCount: 12,
            },
          },
          {
            id: 'mock-2',
            userId: user.id,
            jobId: 'job-2',
            profileId: selectedProfileId,
            matchScore: 0.87,
            applied: false,
            createdAt: new Date(),
            job_listing: {
              id: 'job-2',
              platform: 'indeed',
              jobId: 'indeed-456',
              title: 'Frontend Developer',
              company: 'StartupXYZ',
              location: 'Austin, TX',
              url: 'https://example.com/job2',
              isActive: true,
              isProcessing: false,
              createdAt: new Date(),
              lastCheckedAt: new Date(),
              employmentType: 'Full-time',
              remoteType: 'Hybrid',
              experienceLevel: 'Mid-level',
              description: 'Join our growing team as a frontend developer...',
              postedDate: new Date(),
              closedAt: null,
              applyLink: 'https://example.com/apply2',
              benefits: ['Health Insurance', 'Stock Options'],
              requirements: ['React', 'CSS', '3+ years experience'],
              salary: '$90,000 - $120,000',
              salaryMin: 90000,
              salaryMax: 120000,
              yearsOfExperience: 3,
              companySize: 'Small',
              industryTags: ['Technology', 'Startup'],
              techStack: ['React', 'CSS', 'JavaScript'],
              normalizedTitle: 'frontend developer',
              seniorityLevel: 'Mid-level',
              isRemoteFriendly: true,
              requirementScore: 78,
              popularityScore: 3.8,
              lastMatchedAt: new Date(),
              matchCount: 8,
            },
          },
          {
            id: 'mock-3',
            userId: user.id,
            jobId: 'job-3',
            profileId: selectedProfileId,
            matchScore: 0.73,
            applied: false,
            createdAt: new Date(),
            job_listing: {
              id: 'job-3',
              platform: 'glassdoor',
              jobId: 'glassdoor-789',
              title: 'Full Stack Developer',
              company: 'Enterprise Solutions LLC',
              location: 'New York, NY',
              url: 'https://example.com/job3',
              isActive: true,
              isProcessing: false,
              createdAt: new Date(),
              lastCheckedAt: new Date(),
              employmentType: 'Full-time',
              remoteType: 'On-site',
              experienceLevel: 'Mid-level',
              description: 'We need a full stack developer for our enterprise solutions...',
              postedDate: new Date(),
              closedAt: null,
              applyLink: 'https://example.com/apply3',
              benefits: ['Health Insurance', 'Dental', 'Vision'],
              requirements: ['Java', 'Spring', 'React', '4+ years experience'],
              salary: '$100,000 - $130,000',
              salaryMin: 100000,
              salaryMax: 130000,
              yearsOfExperience: 4,
              companySize: 'Large',
              industryTags: ['Technology', 'Enterprise'],
              techStack: ['Java', 'Spring', 'React'],
              normalizedTitle: 'full stack developer',
              seniorityLevel: 'Mid-level',
              isRemoteFriendly: false,
              requirementScore: 72,
              popularityScore: 3.5,
              lastMatchedAt: new Date(),
              matchCount: 15,
            },
          },
          {
            id: 'mock-4',
            userId: user.id,
            jobId: 'job-4',
            profileId: selectedProfileId,
            matchScore: 0.68,
            applied: false,
            createdAt: new Date(),
            job_listing: {
              id: 'job-4',
              platform: 'linkedin',
              jobId: 'linkedin-101',
              title: 'Software Engineer II',
              company: 'MegaCorp',
              location: 'Seattle, WA',
              url: 'https://example.com/job4',
              isActive: true,
              isProcessing: false,
              createdAt: new Date(),
              lastCheckedAt: new Date(),
              employmentType: 'Full-time',
              remoteType: 'Remote',
              experienceLevel: 'Mid-level',
              description: 'Software Engineer II position at MegaCorp...',
              postedDate: new Date(),
              closedAt: null,
              applyLink: 'https://example.com/apply4',
              benefits: ['Health Insurance', '401k', 'Stock Options', 'Unlimited PTO'],
              requirements: ['Python', 'Django', '3+ years experience'],
              salary: '$110,000 - $140,000',
              salaryMin: 110000,
              salaryMax: 140000,
              yearsOfExperience: 3,
              companySize: 'Large',
              industryTags: ['Technology', 'Cloud'],
              techStack: ['Python', 'Django', 'AWS'],
              normalizedTitle: 'software engineer ii',
              seniorityLevel: 'Mid-level',
              isRemoteFriendly: true,
              requirementScore: 68,
              popularityScore: 4.0,
              lastMatchedAt: new Date(),
              matchCount: 20,
            },
          },
        ]
      : matches;

  const mockTotalCount = matches.length === 0 ? 25 : totalCount;

  return {
    user,
    profiles,
    selectedProfileId,
    matches: mockMatches,
    jobAlerts,
    recommendations,
    savedJobs: savedJobsWithListings,
    pagination: {
      page,
      limit,
      totalCount: mockTotalCount,
      totalPages: Math.ceil(mockTotalCount / limit),
      hasMore: skip + mockMatches.length < mockTotalCount,
    },
    recommendationPagination: {
      page: 1,
      limit: 10,
      totalCount: recommendations.length,
      totalPages: 1,
      hasMore: false,
    },
  };
};
