<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import type { PageData } from './$types';

  // Component imports
  import ProfileSelector from './ProfileSelector.svelte';
  import MatchesList from './MatchesList.svelte';
  import MatchDetails from './MatchDetails.svelte';
  import AlertsList from './AlertsList.svelte';
  import CreateAlertDialog from './CreateAlertDialog.svelte';
  import SavedJobCard from './SavedJobCard.svelte';
  import EmptyState from '$components/shared/EmptyState.svelte';
  import JobMatchDetails from '$components/ai/JobMatchDetails.svelte';

  // UI component imports
  import * as Tabs from '$lib/components/ui/tabs';
  import { Button } from '$lib/components/ui/button';
  import {
    Plus,
    ChevronRight,
    CheckCircle,
    Bookmark,
    Bell,
    Shield,
    Info,
    AlertCircle,
    RefreshCw,
  } from 'lucide-svelte';
  import { goto } from '$app/navigation';

  export let data: PageData;

  // Valid tab values
  const VALID_TABS = ['matches', 'saved', 'alerts', 'recommendations'];

  // Get the active tab from the URL
  let activeTab = 'matches';

  if (typeof window !== 'undefined') {
    const url = new URL(window.location.href);
    const tabParam = url.searchParams.get('tab');
    if (tabParam && VALID_TABS.includes(tabParam)) {
      activeTab = tabParam;
    }
  }

  // Job selection state
  let selectedJobIndex = 0;

  // Saved jobs state
  let savedJobs = data.savedJobs || [];
  let savedJobsError = null;

  // Alert dialog state
  let showCreateAlertDialog = false;

  // SEO metadata based on active tab
  function getPageTitle(tab: string): string {
    switch (tab) {
      case 'saved':
        return 'Saved Jobs';
      case 'alerts':
        return 'Job Alerts';
      case 'recommendations':
        return 'AI Recommendations';
      default:
        return 'Job Matches';
    }
  }

  function getPageDescription(tab: string): string {
    switch (tab) {
      case 'saved':
        return 'View and manage your saved job opportunities';
      case 'alerts':
        return 'Manage your job alerts and notifications';
      case 'recommendations':
        return 'AI-powered job recommendations based on your profile';
      default:
        return 'Jobs matched to your profile based on your resume';
    }
  }

  $: pageTitle = getPageTitle(activeTab);
  $: pageDescription = getPageDescription(activeTab);

  // Event handlers
  function handleJobSelect(event: CustomEvent<{ index: number }>) {
    selectedJobIndex = event.detail.index;
  }

  // Track if we've already loaded saved jobs
  let savedJobsLoaded = false;

  function handleTabChange(value: string) {
    activeTab = value;
    selectedJobIndex = 0;

    // Only fetch saved jobs if we haven't loaded them yet
    if (value === 'saved' && !savedJobsLoaded) {
      fetchSavedJobs();
      // Note: savedJobsLoaded is set in fetchSavedJobs
    }

    updateUrlWithTab(value);
  }

  function handleCreateAlert() {
    showCreateAlertDialog = true;
  }

  function handleAlertCreated() {
    showCreateAlertDialog = false;
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }

  function handleAlertDialogClose() {
    showCreateAlertDialog = false;
  }

  // API calls
  async function fetchSavedJobs() {
    savedJobsError = null;

    try {
      const response = await fetch('/api/saved-jobs');
      if (!response.ok) {
        throw new Error('Failed to fetch saved jobs');
      }

      const result = await response.json();
      savedJobs = result.savedJobs || [];
      // Mark as loaded successfully
      savedJobsLoaded = true;
    } catch (error) {
      console.error('Error fetching saved jobs:', error);
      savedJobsError = error.message;
      // Reset the loaded flag so we can try again
      savedJobsLoaded = false;
    }
  }

  async function removeSavedJob(jobId: string) {
    try {
      const response = await fetch(`/api/jobs/${jobId}/save`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update local state instead of fetching all saved jobs again
        savedJobs = savedJobs.filter((job) => job.jobId !== jobId);
      } else {
        const result = await response.json();
        throw new Error(result.error || 'Failed to remove job');
      }
    } catch (error) {
      console.error('Error removing saved job:', error);
      // Could show a toast notification here
    }
  }

  // Helper functions
  function updateUrlWithTab(tab: string) {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('tab', tab);
      window.history.replaceState({}, '', url.toString());
    }
  }
</script>

<SEO
  title="{pageTitle} | Hirli"
  description={pageDescription}
  keywords="job matches, saved jobs, job alerts, AI recommendations, career matches, job opportunities, skill matching, resume matching" />

<!-- Tabs for different sections -->
<Tabs.Root value={activeTab} onValueChange={handleTabChange}>
  <div class="p-0">
    <Tabs.List class="border-t-0">
      <Tabs.Trigger value="matches" class="flex-1 gap-2">
        <CheckCircle class="h-4 w-4" />
        <span>Job Matches</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="saved" class="flex-1 gap-2">
        <Bookmark class="h-4 w-4" />
        <span>Saved Jobs</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="alerts" class="flex-1 gap-2">
        <Bell class="h-4 w-4" />
        <span>Job Alerts</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="recommendations" class="flex-1 gap-2">
        <Shield class="h-4 w-4" />
        <span>AI Recommendations</span>
      </Tabs.Trigger>
    </Tabs.List>
  </div>

  <!-- Matches Tab -->
  <Tabs.Content value="matches" class="p-4">
    <div class="flex-end mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold">Your Job Matches</h2>
      <ProfileSelector profiles={data.profiles} selectedProfileId={data.selectedProfileId} />
    </div>

    {#if data.profiles.length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No profiles found"
          description="Create a profile to get job matches based on your resume."
          actionText="Create Profile"
          actionHref="/dashboard/settings/profile" />
      </div>
    {:else if data.matches.length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No job matches found"
          description="Upload a resume to your profile to get job matches based on your skills and experience."
          actionText="Upload Resume"
          actionHref="/dashboard/documents" />
      </div>
    {:else}
      <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
        <!-- Job matches list -->
        <div class="md:col-span-1">
          <div class="rounded-lg border p-4">
            <MatchesList
              matches={data.matches}
              pagination={data.pagination}
              selectedProfileId={data.selectedProfileId}
              on:select={handleJobSelect} />
          </div>
        </div>

        <!-- Selected job details -->
        <div class="rounded-lg border p-6 md:col-span-2">
          {#if data.matches[selectedJobIndex]?.job_listing}
            <div class="space-y-6">
              <MatchDetails selectedJob={data.matches[selectedJobIndex]?.job_listing || null} />

              <!-- AI Job Match Analysis -->
              <div class="mt-6 border-t pt-6">
                <h3 class="mb-4 text-lg font-semibold">AI Job Match Analysis</h3>
                <JobMatchDetails
                  profileId={data.selectedProfileId}
                  jobId={data.matches[selectedJobIndex]?.job_listing?.id}
                  jobTitle={data.matches[selectedJobIndex]?.job_listing?.title}
                  company={data.matches[selectedJobIndex]?.job_listing?.company} />
              </div>
            </div>
          {:else}
            <div class="flex h-full items-center justify-center">
              <p class="text-gray-500">Select a job to view details</p>
            </div>
          {/if}
        </div>
      </div>
    {/if}
  </Tabs.Content>

  <!-- Saved Jobs Tab -->
  <Tabs.Content value="saved" class="p-4">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold">Your Saved Jobs</h2>
      <div class="flex gap-2">
        <Button variant="outline" onclick={fetchSavedJobs} class="flex items-center gap-2">
          <RefreshCw class="h-4 w-4" />
          <span>Refresh</span>
        </Button>
        <Button
          variant="outline"
          onclick={() => goto('/dashboard/jobs')}
          class="flex items-center gap-2">
          <ChevronRight class="h-4 w-4" />
          <span>Browse Jobs</span>
        </Button>
      </div>
    </div>

    <!-- Error state -->
    {#if savedJobsError}
      <div class="rounded-lg border border-red-200 bg-red-50 p-6">
        <div class="flex flex-col items-center space-y-2 text-center">
          <AlertCircle class="h-6 w-6 text-red-500" />
          <h3 class="text-lg font-medium text-red-800">Error loading saved jobs</h3>
          <p class="text-sm text-red-600">{savedJobsError}</p>
          <Button variant="outline" class="mt-4" onclick={fetchSavedJobs}>Try Again</Button>
        </div>
      </div>

      <!-- Jobs list -->
    {:else if savedJobs.length > 0}
      <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {#each savedJobs as savedJob}
          {@const job = savedJob.job_listing}
          {#if job}
            <SavedJobCard {job} on:remove={(e) => removeSavedJob(e.detail.jobId)} />
          {/if}
        {/each}
      </div>

      <!-- Empty state -->
    {:else}
      <EmptyState
        title="No saved jobs"
        description="Save jobs that interest you to keep track of opportunities."
        actionText="Browse Jobs"
        actionHref="/dashboard/jobs" />
    {/if}
  </Tabs.Content>

  <!-- Alerts Tab -->
  <Tabs.Content value="alerts" class="p-4">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold">Your Job Alerts</h2>
      <Button variant="default" onclick={handleCreateAlert} class="flex items-center gap-2">
        <Plus class="h-4 w-4" />
        <span>Create Alert</span>
      </Button>
    </div>
    <div class="rounded-lg border p-6">
      <AlertsList alerts={data.jobAlerts || []} onCreateAlert={handleCreateAlert} />
    </div>
  </Tabs.Content>

  <!-- Recommendations Tab -->
  <Tabs.Content value="recommendations" class="p-4">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold">AI Job Recommendations</h2>
      <div class="flex items-center gap-2 text-sm text-gray-500">
        <ProfileSelector profiles={data.profiles} selectedProfileId={data.selectedProfileId} />
      </div>
    </div>

    {#if data.profiles.length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No profiles found"
          description="Create a profile to get AI job recommendations based on your resume."
          actionText="Create Profile"
          actionHref="/dashboard/settings/profile" />
      </div>
    {:else if data.recommendations && data.recommendations.length > 0}
      <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
        <!-- Recommendations list -->
        <div class="md:col-span-1">
          <div class="rounded-lg border p-4">
            <MatchesList
              matches={data.recommendations}
              pagination={data.recommendationPagination || {
                page: 1,
                limit: 20,
                totalCount: data.recommendations.length,
                totalPages: 1,
                hasMore: false,
              }}
              selectedProfileId={data.selectedProfileId}
              on:select={handleJobSelect} />
          </div>
        </div>

        <!-- Selected job details -->
        <div class="rounded-lg border p-6 md:col-span-2">
          {#if data.recommendations[selectedJobIndex]?.job_listing}
            <div class="space-y-6">
              <MatchDetails
                selectedJob={data.recommendations[selectedJobIndex]?.job_listing || null} />

              <!-- AI Job Match Analysis -->
              <div class="mt-6 border-t pt-6">
                <h3 class="mb-4 text-lg font-semibold">AI Job Match Analysis</h3>
                <JobMatchDetails
                  profileId={data.selectedProfileId}
                  jobId={data.recommendations[selectedJobIndex]?.job_listing?.id}
                  jobTitle={data.recommendations[selectedJobIndex]?.job_listing?.title}
                  company={data.recommendations[selectedJobIndex]?.job_listing?.company} />
              </div>
            </div>
          {:else}
            <div class="flex h-full items-center justify-center">
              <p class="text-gray-500">Select a job to view details</p>
            </div>
          {/if}
        </div>
      </div>
    {:else}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No AI recommendations yet"
          description="Our AI is working on finding the best job matches for your profile. Check back soon!"
          actionText="View Job Matches"
          actionHref="#"
          on:click={() => handleTabChange('matches')} />
      </div>
    {/if}
  </Tabs.Content>
</Tabs.Root>

<!-- Create Alert Dialog -->
{#if showCreateAlertDialog}
  <CreateAlertDialog
    onClose={handleAlertDialogClose}
    onCreated={handleAlertCreated}
    userId={data.user?.id} />
{/if}
