<script lang="ts">
  import SEO from '$components/shared/SEO.svelte';
  import type { PageData } from './$types';

  // Component imports
  import ProfileSelector from './ProfileSelector.svelte';
  import MatchesList from './MatchesList.svelte';
  import MatchDetails from './MatchDetails.svelte';
  import AlertsList from './AlertsList.svelte';
  import CreateAlertDialog from './CreateAlertDialog.svelte';
  import SavedJobCard from './SavedJobCard.svelte';
  import EmptyState from '$components/shared/EmptyState.svelte';
  import JobMatchDetails from '$components/ai/JobMatchDetails.svelte';

  // UI component imports
  import * as Tabs from '$lib/components/ui/tabs';
  import { Button } from '$lib/components/ui/button';
  import * as Card from '$lib/components/ui/card';
  import * as Select from '$lib/components/ui/select';
  import * as Tooltip from '$lib/components/ui/tooltip';
  import { Badge } from '$lib/components/ui/badge';
  import {
    Plus,
    ChevronRight,
    CheckCircle,
    Bookmark,
    Bell,
    Shield,
    AlertCircle,
    RefreshCw,
    Info,
    X,
  } from 'lucide-svelte';
  import { goto } from '$app/navigation';
  import { toast } from 'svelte-sonner';

  let { data }: { data: PageData } = $props();

  // Reactive state for filters
  let matchScoreFilter = $state('');
  let locationFilter = $state('');
  let sortOption = $state('match');

  // Valid tab values
  const VALID_TABS = ['matches', 'saved', 'alerts', 'recommendations'];

  // Get the active tab from the URL
  let activeTab = $state('matches');

  if (typeof window !== 'undefined') {
    const url = new URL(window.location.href);
    const tabParam = url.searchParams.get('tab');
    if (tabParam && VALID_TABS.includes(tabParam)) {
      activeTab = tabParam;
    }
  }

  // Job selection state
  let selectedJobIndex = $state(0);

  // Saved jobs state
  let savedJobs = $state(data.savedJobs || []);
  let savedJobsError = $state(null);

  // Alert dialog state
  let showCreateAlertDialog = $state(false);

  // SEO metadata based on active tab
  function getPageTitle(tab: string): string {
    switch (tab) {
      case 'saved':
        return 'Saved Jobs';
      case 'alerts':
        return 'Job Alerts';
      case 'recommendations':
        return 'AI Recommendations';
      default:
        return 'Job Matches';
    }
  }

  function getPageDescription(tab: string): string {
    switch (tab) {
      case 'saved':
        return 'View and manage your saved job opportunities';
      case 'alerts':
        return 'Manage your job alerts and notifications';
      case 'recommendations':
        return 'AI-powered job recommendations based on your profile';
      default:
        return 'Jobs matched to your profile based on your resume';
    }
  }

  let pageTitle = $derived(getPageTitle(activeTab));
  let pageDescription = $derived(getPageDescription(activeTab));

  // Event handlers
  function handleJobSelect(event: CustomEvent<{ index: number }>) {
    selectedJobIndex = event.detail.index;
  }

  // Track if we've already loaded saved jobs
  let savedJobsLoaded = false;

  function handleTabChange(value: string) {
    activeTab = value;
    selectedJobIndex = 0;

    // Only fetch saved jobs if we haven't loaded them yet
    if (value === 'saved' && !savedJobsLoaded) {
      fetchSavedJobs();
      // Note: savedJobsLoaded is set in fetchSavedJobs
    }

    updateUrlWithTab(value);
  }

  function handleCreateAlert() {
    showCreateAlertDialog = true;
  }

  function handleAlertCreated() {
    showCreateAlertDialog = false;
    if (typeof window !== 'undefined') {
      window.location.reload();
    }
  }

  function handleAlertDialogClose() {
    showCreateAlertDialog = false;
  }

  // API calls
  async function fetchSavedJobs() {
    savedJobsError = null;

    try {
      const response = await fetch('/api/saved-jobs');
      if (!response.ok) {
        throw new Error('Failed to fetch saved jobs');
      }

      const result = await response.json();
      savedJobs = result.savedJobs || [];
      // Mark as loaded successfully
      savedJobsLoaded = true;
    } catch (error) {
      console.error('Error fetching saved jobs:', error);
      savedJobsError = error.message;
      // Reset the loaded flag so we can try again
      savedJobsLoaded = false;
    }
  }

  async function removeSavedJob(jobId: string) {
    try {
      const response = await fetch(`/api/jobs/${jobId}/save`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Update local state instead of fetching all saved jobs again
        savedJobs = savedJobs.filter((job) => job.jobId !== jobId);
      } else {
        const result = await response.json();
        throw new Error(result.error || 'Failed to remove job');
      }
    } catch (error) {
      console.error('Error removing saved job:', error);
      // Could show a toast notification here
    }
  }

  // Helper functions
  function updateUrlWithTab(tab: string) {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('tab', tab);
      window.history.replaceState({}, '', url.toString());
    }
  }
</script>

<SEO
  title="{pageTitle} | Hirli"
  description={pageDescription}
  keywords="job matches, saved jobs, job alerts, AI recommendations, career matches, job opportunities, skill matching, resume matching" />

<!-- Tabs for different sections -->
<Tabs.Root value={activeTab} onValueChange={handleTabChange}>
  <div class="p-0">
    <Tabs.List class="border-t-0">
      <Tabs.Trigger value="matches" class="flex-1 gap-2">
        <CheckCircle class="h-4 w-4" />
        <span>Job Matches</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="saved" class="flex-1 gap-2">
        <Bookmark class="h-4 w-4" />
        <span>Saved Jobs</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="alerts" class="flex-1 gap-2">
        <Bell class="h-4 w-4" />
        <span>Job Alerts</span>
      </Tabs.Trigger>
      <Tabs.Trigger value="recommendations" class="flex-1 gap-2">
        <Shield class="h-4 w-4" />
        <span>AI Recommendations</span>
      </Tabs.Trigger>
    </Tabs.List>
  </div>

  <!-- Matches Tab -->
  <Tabs.Content value="matches" class="p-4">
    <!-- Filters and Profile Selector -->
    <div class="mb-6 space-y-4">
      <!-- Top Row: Filters and Profile Selector -->
      <div class="flex flex-wrap items-center justify-between gap-4">
        <!-- Filters Section -->
        <div class="flex flex-wrap items-center gap-4">
          <div class="flex items-center gap-2">
            <Select.Root
              type="single"
              value={matchScoreFilter}
              onValueChange={(value) => {
                matchScoreFilter = value || '';
              }}>
              <Select.Trigger class="w-[180px]">
                <Select.Value placeholder="Match Score" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="">All Matches</Select.Item>
                <Select.Item value="90">90%+ (Excellent)</Select.Item>
                <Select.Item value="80">80-89% (Great)</Select.Item>
                <Select.Item value="70">70-79% (Good)</Select.Item>
                <Select.Item value="60">60-69% (Fair)</Select.Item>
              </Select.Content>
            </Select.Root>
          </div>

          <div class="flex items-center gap-2">
            <Select.Root
              type="single"
              value={locationFilter}
              onValueChange={(value) => {
                locationFilter = value || '';
              }}>
              <Select.Trigger class="w-[140px]">
                <Select.Value placeholder="Location" />
              </Select.Trigger>
              <Select.Content>
                <Select.Item value="">All Locations</Select.Item>
                <Select.Item value="remote">Remote</Select.Item>
                <Select.Item value="hybrid">Hybrid</Select.Item>
                <Select.Item value="onsite">On-site</Select.Item>
              </Select.Content>
            </Select.Root>
          </div>

          <div>
            <Select.Root
              type="single"
              value={sortOption}
              onValueChange={(value) => {
                sortOption = value;
              }}>
              <Select.Trigger class="w-[140px]">
                <Select.Value placeholder={sortOption ? sortOption : 'Best Match'} />
              </Select.Trigger>
              <Select.Content class="w-[140px]">
                <Select.Item value="match">Best Match</Select.Item>
                <Select.Item value="newest">Newest First</Select.Item>
                <Select.Item value="salary">Highest Salary</Select.Item>
                <Select.Item value="company">Company A-Z</Select.Item>
              </Select.Content>
            </Select.Root>
          </div>

          <!-- Results Badge with Info Tooltip -->
          <div class="flex items-center gap-2">
            <Badge variant="secondary" class="text-sm">
              {data.matches.length} matches found
            </Badge>
            <Tooltip.Provider>
              <Tooltip.Root>
                <Tooltip.Trigger>
                  <Info class="text-muted-foreground h-4 w-4 cursor-help" />
                </Tooltip.Trigger>
                <Tooltip.Content>
                  <p>Free plan: 15 matches per day</p>
                </Tooltip.Content>
              </Tooltip.Root>
            </Tooltip.Provider>
          </div>
        </div>

        <!-- Profile Selector -->
        <ProfileSelector profiles={data.profiles} selectedProfileId={data.selectedProfileId} />
      </div>

      <!-- Quick Filter Chips -->
      <div class="flex flex-wrap gap-2">
        <Button variant="outline" size="sm" class="h-8">High Match (90%+)</Button>
        <Button variant="outline" size="sm" class="h-8">Remote Only</Button>
        <Button variant="outline" size="sm" class="h-8">Tech Companies</Button>
        <Button variant="outline" size="sm" class="h-8">$100k+ Salary</Button>
      </div>
    </div>

    {#if data.profiles.length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No profiles found"
          description="Create a profile to get job matches based on your resume."
          actionText="Create Profile"
          actionHref="/dashboard/settings/profile" />
      </div>
    {:else if data.matches.length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No job matches found"
          description="Upload a resume to your profile to get job matches based on your skills and experience."
          actionText="Upload Resume"
          actionHref="/dashboard/documents" />
      </div>
    {:else}
      <!-- Job Matches Grid -->
      <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {#each data.matches as match}
          {@const job = match.job_listing}
          {@const matchScore = Math.round(match.matchScore * 100)}
          {@const scoreColor =
            matchScore >= 90
              ? 'bg-green-100 text-green-800 border-green-200'
              : matchScore >= 80
                ? 'bg-blue-100 text-blue-800 border-blue-200'
                : matchScore >= 70
                  ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                  : 'bg-gray-100 text-gray-800 border-gray-200'}

          <Card.Root
            class="hover:border-primary/50 group relative transition-all hover:shadow-md">
            <!-- Top Right Action Icons -->
            <div
              class="absolute right-2 top-2 z-10 flex gap-1"
              role="group"
              aria-label="Job actions">
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    <Button
                      size="sm"
                      variant="ghost"
                      class="hover:bg-background/80 h-6 w-6 p-0"
                      onclick={() => {
                        /* Handle save */
                        toast.success('Job saved!');
                      }}>
                      <Bookmark class="h-3 w-3" />
                    </Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    <p>Save job</p>
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>

              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    <Button
                      size="sm"
                      variant="ghost"
                      class="hover:bg-background/80 h-6 w-6 p-0"
                      onclick={() => {
                        /* Handle dismiss */
                        toast.success('Job dismissed');
                      }}>
                      <X class="h-3 w-3" />
                    </Button>
                  </Tooltip.Trigger>
                  <Tooltip.Content>
                    <p>Dismiss job</p>
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
            </div>

            <!-- Match Score Badge with Tooltip -->
            <div class="absolute -left-2 -top-2 z-10">
              <Tooltip.Provider>
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    <Badge
                      class="border-background rounded-full border-2 px-2 py-1 text-xs font-bold {scoreColor}">
                      {matchScore}%
                    </Badge>
                  </Tooltip.Trigger>
                  <Tooltip.Content class="max-w-xs">
                    <div class="space-y-1">
                      <p class="font-medium">Why this matches:</p>
                      <ul class="space-y-0.5 text-xs">
                        <li>• Skills match: {Math.round(matchScore * 0.8)}%</li>
                        <li>• Experience level: {job.experienceLevel || 'Mid-level'}</li>
                        <li>• Location preference: {job.remoteType || 'On-site'}</li>
                        {#if job.techStack && job.techStack.length > 0}
                          <li>• Tech stack: {job.techStack.slice(0, 2).join(', ')}</li>
                        {/if}
                      </ul>
                    </div>
                  </Tooltip.Content>
                </Tooltip.Root>
              </Tooltip.Provider>
            </div>

            <Card.Content class="p-4 pt-6">
              <!-- Job Content -->
              <div class="space-y-3">
                <div>
                  <h3
                    class="group-hover:text-primary line-clamp-2 text-sm font-semibold leading-tight transition-colors">
                    {job.title}
                  </h3>
                  <p class="text-muted-foreground text-sm font-medium">{job.company}</p>
                </div>

                <!-- Key Benefits/Highlights -->
                {#if job.benefits && job.benefits.length > 0}
                  <div class="flex flex-wrap gap-1">
                    {#each job.benefits.slice(0, 3) as benefit}
                      <Badge variant="outline" class="px-1.5 py-0.5 text-xs">
                        {benefit}
                      </Badge>
                    {/each}
                  </div>
                {/if}

                <div class="text-muted-foreground space-y-1 text-xs">
                  <p class="flex items-center gap-1">
                    <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                    {job.location}
                  </p>
                  {#if job.salaryMin && job.salaryMax}
                    <p class="flex items-center gap-1">
                      <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                      ${job.salaryMin?.toLocaleString()} - ${job.salaryMax?.toLocaleString()}
                    </p>
                  {:else if job.salary}
                    <p class="flex items-center gap-1">
                      <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                      {job.salary}
                    </p>
                  {/if}
                  {#if job.employmentType}
                    <p class="flex items-center gap-1">
                      <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                      {job.employmentType}
                    </p>
                  {/if}
                  {#if job.remoteType}
                    <p class="flex items-center gap-1">
                      <span class="inline-block h-1 w-1 rounded-full bg-current"></span>
                      {job.remoteType}
                    </p>
                  {/if}
                </div>

                <!-- Apply Button -->
                <div class="pt-2" onclick={(e) => e.stopPropagation()}>
                  <Button
                    size="sm"
                    class="h-8 w-full text-xs"
                    onclick={() => goto(`/dashboard/jobs/${job.id}`)}>
                    Apply Now
                  </Button>
                </div>
              </div>
            </Card.Content>
          </Card.Root>
        {/each}
      </div>

      <!-- Pagination -->
      {#if data.pagination.totalPages > 1}
        <div class="mt-8 flex items-center justify-center gap-2">
          <Button
            variant="outline"
            disabled={data.pagination.page === 1}
            onclick={() =>
              goto(
                `/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.page - 1}`
              )}>
            Previous
          </Button>
          <span class="text-muted-foreground px-4 text-sm">
            Page {data.pagination.page} of {data.pagination.totalPages}
          </span>
          <Button
            variant="outline"
            disabled={!data.pagination.hasMore}
            onclick={() =>
              goto(
                `/dashboard/matches?profileId=${data.selectedProfileId}&page=${data.pagination.page + 1}`
              )}>
            Next
          </Button>
        </div>
      {/if}
    {/if}
  </Tabs.Content>

  <!-- Saved Jobs Tab -->
  <Tabs.Content value="saved" class="p-4">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold">Your Saved Jobs</h2>
      <div class="flex gap-2">
        <Button variant="outline" onclick={fetchSavedJobs} class="flex items-center gap-2">
          <RefreshCw class="h-4 w-4" />
          <span>Refresh</span>
        </Button>
        <Button
          variant="outline"
          onclick={() => goto('/dashboard/jobs')}
          class="flex items-center gap-2">
          <ChevronRight class="h-4 w-4" />
          <span>Browse Jobs</span>
        </Button>
      </div>
    </div>

    <!-- Error state -->
    {#if savedJobsError}
      <div class="rounded-lg border border-red-200 bg-red-50 p-6">
        <div class="flex flex-col items-center space-y-2 text-center">
          <AlertCircle class="h-6 w-6 text-red-500" />
          <h3 class="text-lg font-medium text-red-800">Error loading saved jobs</h3>
          <p class="text-sm text-red-600">{savedJobsError}</p>
          <Button variant="outline" class="mt-4" onclick={fetchSavedJobs}>Try Again</Button>
        </div>
      </div>

      <!-- Jobs list -->
    {:else if savedJobs.length > 0}
      <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {#each savedJobs as savedJob}
          {@const job = savedJob.job_listing}
          {#if job}
            <SavedJobCard {job} on:remove={(e) => removeSavedJob(e.detail.jobId)} />
          {/if}
        {/each}
      </div>

      <!-- Empty state -->
    {:else}
      <EmptyState
        title="No saved jobs"
        description="Save jobs that interest you to keep track of opportunities."
        actionText="Browse Jobs"
        actionHref="/dashboard/jobs" />
    {/if}
  </Tabs.Content>

  <!-- Alerts Tab -->
  <Tabs.Content value="alerts" class="p-4">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold">Your Job Alerts</h2>
      <Button variant="default" onclick={handleCreateAlert} class="flex items-center gap-2">
        <Plus class="h-4 w-4" />
        <span>Create Alert</span>
      </Button>
    </div>
    <div class="rounded-lg border p-6">
      <AlertsList alerts={data.jobAlerts || []} onCreateAlert={handleCreateAlert} />
    </div>
  </Tabs.Content>

  <!-- Recommendations Tab -->
  <Tabs.Content value="recommendations" class="p-4">
    <div class="mb-4 flex items-center justify-between">
      <h2 class="text-lg font-semibold">AI Job Recommendations</h2>
      <div class="flex items-center gap-2 text-sm text-gray-500">
        <ProfileSelector profiles={data.profiles} selectedProfileId={data.selectedProfileId} />
      </div>
    </div>

    {#if data.profiles.length === 0}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No profiles found"
          description="Create a profile to get AI job recommendations based on your resume."
          actionText="Create Profile"
          actionHref="/dashboard/settings/profile" />
      </div>
    {:else if data.recommendations && data.recommendations.length > 0}
      <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
        <!-- Recommendations list -->
        <div class="md:col-span-1">
          <div class="rounded-lg border p-4">
            <MatchesList
              matches={data.recommendations}
              pagination={data.recommendationPagination || {
                page: 1,
                limit: 20,
                totalCount: data.recommendations.length,
                totalPages: 1,
                hasMore: false,
              }}
              selectedProfileId={data.selectedProfileId}
              on:select={handleJobSelect} />
          </div>
        </div>

        <!-- Selected job details -->
        <div class="rounded-lg border p-6 md:col-span-2">
          {#if data.recommendations[selectedJobIndex]?.job_listing}
            <div class="space-y-6">
              <MatchDetails
                selectedJob={data.recommendations[selectedJobIndex]?.job_listing || null} />

              <!-- AI Job Match Analysis -->
              <div class="mt-6 border-t pt-6">
                <h3 class="mb-4 text-lg font-semibold">AI Job Match Analysis</h3>
                <JobMatchDetails
                  profileId={data.selectedProfileId}
                  jobId={data.recommendations[selectedJobIndex]?.job_listing?.id}
                  jobTitle={data.recommendations[selectedJobIndex]?.job_listing?.title}
                  company={data.recommendations[selectedJobIndex]?.job_listing?.company} />
              </div>
            </div>
          {:else}
            <div class="flex h-full items-center justify-center">
              <p class="text-gray-500">Select a job to view details</p>
            </div>
          {/if}
        </div>
      </div>
    {:else}
      <div class="rounded-lg border p-6">
        <EmptyState
          title="No AI recommendations yet"
          description="Our AI is working on finding the best job matches for your profile. Check back soon!"
          actionText="View Job Matches"
          actionHref="#"
          on:click={() => handleTabChange('matches')} />
      </div>
    {/if}
  </Tabs.Content>
</Tabs.Root>

<!-- Create Alert Dialog -->
{#if showCreateAlertDialog}
  <CreateAlertDialog
    onClose={handleAlertDialogClose}
    onCreated={handleAlertCreated}
    userId={data.user?.id} />
{/if}
